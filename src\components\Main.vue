<!--首页-->
<template>
    <div id="mCom" class="mCom" v-loading="showLoading" element-loading-text="拼命加载中...">
        <div class="homeMain">
            <div slot="header" class="fx">
                <el-button type="primary" @click="fastAddProject">
                    <div class="apCom">
                        <i class="fa fa-plus"></i>
                        <span>新建工程</span>
                    </div>
                </el-button>
                <el-form label-width="80px" inline class="main-form">

                    <el-form-item label="名称">
                        <el-input placeholder="输入名称过滤" v-model="filterStr" class="search" :maxlength="30">
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" circle @click="loadMore"></el-button>
                        <el-button icon="el-icon-refresh" circle @click="resetSearch"></el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card id="sl" class="tBlock">
                <el-row :gutter="20" id="" class="pList">
                    <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="4" v-for="item in templateList" :key="item.id">
                        <MainTemplateItem :item="item"></MainTemplateItem>
                    </el-col>
                </el-row>
            </el-card>



        </div>
        <div class="pagesize">
            <div id="fy" class="block">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="pagination.currentPage" :page-sizes="[10, 20, 30, 50]"
                    :page-size="pagination.perSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.totalSize" v-if='pagination.totalSize > 0'></el-pagination>
            </div>
        </div>
        <CopyTemplateAsProject></CopyTemplateAsProject>
        <FastCreateProjectDialog></FastCreateProjectDialog>
    </div>
</template>
<style scoped>
.fx {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

/* 响应式布局优化 */
.homeMain {
  min-height: calc(100vh - 200px);
  height: auto !important; /* 覆盖全局样式，允许内容撑开 */
  padding: 0 10px;
  overflow: visible; /* 确保内容不被隐藏 */
}

.tBlock {
  width: 100%;
  overflow: visible;
}

.pList {
  width: 100%;
}

/* 分页区域样式 */
.pagesize {
  margin-top: 20px;
  padding: 10px 0;
}

.pagesize .block {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 响应式媒体查询 */
@media (max-width: 1200px) {
  .fx {
    flex-direction: column;
    align-items: stretch;
  }

  .fx .el-button {
    margin-bottom: 10px;
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .homeMain {
    padding: 0 5px;
  }

  .fx {
    gap: 5px;
  }

  .main-form {
    width: 100%;
  }

  .main-form .el-form-item {
    margin-bottom: 10px;
  }
}

@media (max-width: 576px) {
  .pList {
    margin: 0 -5px;
  }

  .pagesize .block {
    overflow-x: auto;
    padding: 0 10px;
  }
}
</style>
<script>
import { mapState, mapActions, mapMutations } from "vuex";
import FastCreateProjectDialog from "./project/project_menu/project_tree/FastCreateProjectDialog.vue";
import CopyTemplateAsProject from "./project/project_menu/project_tree/CopyTemplateAsProject.vue";
import MainTemplateItem from "./main/MainTemplateItem.vue";

export default {
  name: "Main",
  data: function() {
    return {
      // mComStyle: {//组件绘制时动态设置容器的长宽
      //   width: "",
      //   height: ""
      // },
      filterStr: "",
      showLoading: true
    };
  },
  beforeMount: function() {
    //挂载前调用
    this.$store.dispatch("checkToken");
    this.showLoading = false;
  },
  created: function() {
    // var w = document.documentElement.clientWidth;
    // var h = document.documentElement.clientHeight;
    // this.mComStyle.width = w - 90 + 'px';
    // this.mComStyle.height = h - 87 + 'px';
    this.getTemplateList(); // 获取模板列表，使用映射的 action
    this.$store.dispatch("getMenuProjectList", 0); //获取工程根节点
    // this.$store.dispatch("getBBSList");//获取社区列表
    // this.$store.dispatch("getDocumentsList");//获取入门列表
    var routerPath = this.$router.currentRoute.path;
    var curMenu = this.$store.state.global.navMenu.filter(
      node => node.linkTo == routerPath
    );
    this.$store.state.global.curMenuItem = curMenu[0];
  },
  computed: {
    ...mapState({
      main: state => state.main,
      pagination: state => state.main.pagination, // 映射分页状态
      flowData: state => state.project.flowData,
      rightContentTargetItem: state => state.flowData.rightContentTargetItem
    }),
    templateList: function() {
      //模板列表
      var list = this.main.templateList;
      var result = list.filter(
        node => node.project.name.indexOf(this.filterStr) > -1
      );
      return result;
    }
  },
  components: {
    MainTemplateItem,
    FastCreateProjectDialog,
    CopyTemplateAsProject
  },
  methods: {
    ...mapActions([
      "getTemplateList" // 映射 getTemplateList action
    ]),
    ...mapMutations([
      "updatePagination" // 映射 updatePagination mutation (稍后在 store 中实现)
    ]),
    fastAddProject() {
      //快速新建工程
      if (this.flowData.isRunning) {
        this.$notify({
          type: "warning",
          message: "工程运行中，请稍后再试！"
        });
        return;
      }
      //设置添加位置
      this.flowData.rightContentTargetItem = this.$store.state.project.projectMenu[0];
      //显示弹框
      this.main.showDetail.showFastCreateProjectDialog = true;
    },
    loadMore() {
      //加载更多
      // 修改为调用映射的 getTemplateList action
      this.getTemplateList();
    },
    handleSizeChange(val) {
      // 更新每页条数并加载第一页
      this.updatePagination({ perSize: val, currentPage: 1 });
      this.getTemplateList();
    },
    handleCurrentChange(val) {
      // 更新当前页并加载数据
      this.updatePagination({ currentPage: val });
      this.getTemplateList();
    },
    resetSearch() {
      this.filterStr = "";
      // 重置搜索后加载第一页
      this.updatePagination({ currentPage: 1 });
      this.getTemplateList();
    }
  }
};
</script>
