<template>
    <a href="javascript:void(0)" class="pListLi" @mouseover="showDeleteIcon = true" @mouseout="showDeleteIcon = false">
        <template v-if="true">
            <div class="close" v-show="showDeleteIcon" @click="deleteTemplate($event)"><i
                    class="el-icon-circle-close-outline"></i>
            </div>
        </template>
        <div @click="copyTemplate($event)" class="ppl">
            <div class="header-section">
                <span class="icon">
                    <img src="../../assets/css/images/icon-box.png" alt="">
                </span>
                <div class="title-section">
                    <span class="title">{{ item.project.name }}</span>
                    <span class="tags">{{ item.tags }}</span>
                </div>
            </div>

            <div class="desc" :title="item.project.description">
                {{ item.project.description }}
            </div>

            <div class="footer-section">
                <div class="author">
                    <!-- 圆形头像 使用 creatorName 的第一个字母 -->
                    <span class="avatar">{{ item.project.creatorName.substring(0, 1) }}</span>
                    <!-- <span class="author-name">{{ item.project.creatorName }}</span> -->
                </div>
                <span class="create-time">{{ formattedCreateTime }}</span>
            </div>

            <div class="copy"><span class="animated"><i class="fa fa-clipboard"></i>从模板新建</span></div>
        </div>
    </a>
</template>
<style>
/* 覆盖全局样式 - 防止在不同分辨率下被裁剪 */
.pList .pListLi {
  overflow: visible !important; /* 覆盖全局的 overflow: hidden */
  height: auto !important; /* 允许高度自适应 */
  min-height: 250px !important; /* 保持最小高度 */
}

/* 主容器样式 - 使用垂直flex布局，防止换行 */
.ppl {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;
  /* 确保在不同分辨率下内容不被裁剪 */
  min-height: 250px;
  position: relative;
}

/* 头部区域 - 图标和标题 */
.header-section {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-shrink: 0;
  margin-bottom: 8px;
}

.icon {
  margin-right: 10px;
  flex-shrink: 0;
}

.icon img {
  width: 50px;
  height: 50px;
  display: block;
}

.title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.tags {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 描述区域 */
.desc {
  color: #a7a7a7;
  font-size: 12px;
  line-height: 18px;
  height: 54px;
  overflow: hidden;
  margin: 8px 0;
  flex-shrink: 0;
  /* 限制显示行数 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

/* 底部区域 - 作者和时间 */
.footer-section {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: auto;
  padding: 5px;
  height: 35px; /* 增加高度以适应150%缩放 */
  box-sizing: border-box;
  /* 强制单行显示 */
  white-space: nowrap;
  overflow: visible; /* 改为visible确保内容可见 */
  /* 确保在不同分辨率下不被遮挡 */
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95); /* 添加半透明背景确保可见性 */
}

.author {
  display: flex;
  align-items: center;
  color: #666;
  flex: 0 0 30px; /* 固定宽度，只给avatar留空间 */
  min-width: 30px;
  max-width: 30px; /* 限制最大宽度，为时间留出更多空间 */
  overflow: visible;
}

.avatar {
  display: inline-block;
  width: 24px; /* 增加尺寸以适应150%缩放 */
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 24px;
  margin-right: 6px;
  font-size: 12px;
  flex-shrink: 0;
  /* 确保在高分辨率下可见 */
  min-width: 24px;
  min-height: 24px;
}

.author-name {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px; /* 限制作者名最大宽度 */
}

.create-time {
  color: #999;
  font-size: 11px;
  white-space: nowrap;
  flex: 1; /* 占用剩余空间 */
  text-align: right;
  margin-left: 8px;
  /* 确保有足够空间显示时间 */
  min-width: 0; /* 允许弹性调整 */
  overflow: visible; /* 确保不被裁剪 */
}

/* 复制按钮覆盖层 */
.copy {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: none;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.copy span {
  display: inline-block;
  color: #408fcc;
  padding: 5px 12px;
  line-height: 24px;
}

.copy span i {
  display: block;
  font-size: 36px;
  margin-bottom: 6px;
}

/* 悬停效果 */
.pListLi:hover .copy {
  display: flex;
}

.pListLi:hover .copy span {
  animation: fadeInUps 0.3s;
}

/* 响应式媒体查询 - 与Element UI断点对齐 */

/* xl屏幕: ≥1920px - 超大屏幕 */
@media (min-width: 1920px) {
  .footer-section {
    height: 38px;
    padding: 6px;
  }

  .author {
    flex: 0 0 35px;
    max-width: 35px;
  }

  .avatar {
    width: 26px;
    height: 26px;
    line-height: 26px;
    font-size: 13px;
  }

  .create-time {
    font-size: 12px;
  }
}

/* lg屏幕: 1200px-1919px - 大屏幕 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .ppl {
    padding: 8px;
  }

  .icon img {
    width: 40px;
    height: 40px;
  }

  .title {
    font-size: 13px;
    line-height: 18px;
  }

  .desc {
    height: 45px;
    font-size: 11px;
    line-height: 16px;
  }
}

/* md屏幕: 992px-1199px - 中等屏幕 */
@media (min-width: 992px) and (max-width: 1199px) {
  .ppl {
    padding: 7px;
  }

  .icon img {
    width: 38px;
    height: 38px;
  }

  .title {
    font-size: 12px;
    line-height: 17px;
  }

  .desc {
    height: 42px;
    font-size: 10px;
    line-height: 15px;
  }
}

/* sm屏幕: 768px-991px - 小屏幕 */
@media (min-width: 768px) and (max-width: 991px) {
  .ppl {
    padding: 6px;
  }

  .header-section {
    margin-bottom: 6px;
  }

  .icon {
    margin-right: 8px;
  }

  .icon img {
    width: 35px;
    height: 35px;
  }

  .title {
    font-size: 12px;
    line-height: 16px;
  }

  .tags {
    font-size: 11px;
  }

  .desc {
    height: 40px;
    font-size: 10px;
    line-height: 14px;
    margin: 6px 0;
  }

  .footer-section {
    height: 28px;
    padding: 4px;
  }

  .author {
    flex: 0 0 28px;
    max-width: 28px;
  }

  .avatar {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    margin-right: 4px;
  }

  .create-time {
    font-size: 10px;
    margin-left: 5px;
  }
}

/* xs屏幕: <768px - 超小屏幕 */
@media (max-width: 767px) {
  .ppl {
    padding: 5px;
  }

  .desc {
    height: 35px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .footer-section {
    height: 26px;
    padding: 3px;
    /* 强制单行显示 */
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
  }

  .author {
    flex: 0 0 22px !important;
    max-width: 22px;
    overflow: hidden;
  }

  .avatar {
    width: 18px;
    height: 18px;
    line-height: 18px;
    font-size: 9px;
    margin-right: 2px;
  }

  .create-time {
    font-size: 9px;
    margin-left: 3px;
    flex: 1 !important;
    text-align: right;
  }
}

/* 高分辨率/缩放优化 - 使用更高优先级避免与断点冲突 */

/* 150%缩放优化 - 优先级高于断点媒体查询 */
@media screen and (min-resolution: 144dpi) and (min-width: 768px),
       screen and (min-resolution: 1.5dppx) and (min-width: 768px),
       screen and (-webkit-min-device-pixel-ratio: 1.5) and (min-width: 768px) {
  .footer-section {
    height: 40px !important;
    padding: 6px !important;
  }

  .author {
    flex: 0 0 35px !important;
    max-width: 35px !important;
  }

  .avatar {
    width: 26px !important;
    height: 26px !important;
    line-height: 26px !important;
    font-size: 13px !important;
    margin-right: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  .create-time {
    font-size: 12px !important;
    margin-left: 10px !important;
  }
}

/* 150%缩放 + 小屏幕优化 */
@media screen and (min-resolution: 1.5dppx) and (max-width: 767px) {
  .footer-section {
    height: 30px !important;
    padding: 4px !important;
  }

  .author {
    flex: 0 0 25px !important;
    max-width: 25px !important;
  }

  .avatar {
    width: 20px !important;
    height: 20px !important;
    line-height: 20px !important;
    font-size: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  .create-time {
    font-size: 10px !important;
  }
}

/* 200%缩放优化 */
@media screen and (min-resolution: 192dpi) and (min-width: 768px),
       screen and (min-resolution: 2dppx) and (min-width: 768px),
       screen and (-webkit-min-device-pixel-ratio: 2) and (min-width: 768px) {
  .footer-section {
    height: 45px !important;
    padding: 8px !important;
  }

  .author {
    flex: 0 0 40px !important;
    max-width: 40px !important;
  }

  .avatar {
    width: 28px !important;
    height: 28px !important;
    line-height: 28px !important;
    font-size: 14px !important;
    margin-right: 10px !important;
  }

  .create-time {
    font-size: 13px !important;
    margin-left: 12px !important;
  }
}

/* 专门针对125%、150%、175%分辨率的优化 */
@media screen and (min-resolution: 120dpi) and (max-resolution: 191dpi) {
  /* 覆盖全局样式，确保容器不裁剪内容 */
  .pList .pListLi {
    overflow: visible !important;
    height: auto !important;
    min-height: 280px !important; /* 在中等缩放下增加最小高度 */
  }

  .ppl {
    min-height: 280px !important;
  }

  .footer-section {
    height: 40px !important;
    padding: 6px !important;
    position: relative !important;
    z-index: 15 !important;
    background: rgba(255, 255, 255, 0.98) !important;
    margin-top: 8px !important; /* 确保与上方内容有间距 */
  }

  .author {
    flex: 0 0 35px !important;
    max-width: 35px !important;
  }

  .avatar {
    width: 26px !important;
    height: 26px !important;
    line-height: 26px !important;
    font-size: 13px !important;
    border: 1px solid rgba(64, 158, 255, 0.3) !important;
  }

  .create-time {
    font-size: 12px !important;
    margin-left: 10px !important;
  }
}

/* 专门针对200%及以上分辨率的优化 */
@media screen and (min-resolution: 192dpi) {
  .pList .pListLi {
    overflow: visible !important;
    height: auto !important;
    min-height: 320px !important; /* 在高缩放下进一步增加最小高度 */
  }

  .ppl {
    min-height: 320px !important;
  }

  .footer-section {
    height: 50px !important;
    padding: 8px !important;
    position: relative !important;
    z-index: 20 !important;
    background: rgba(255, 255, 255, 0.98) !important;
    margin-top: 10px !important;
  }
}
</style>
<script>
import { setAppKeyHeaders } from "@/utils/appkey.js";
export default {
  name: "MainTemplateItem",
  props: ["item"],
  data() {
    return {
      showDeleteIcon: false //是否显示删除图标
    };
  },
  computed: {
    menuMain: function() {
      return this.$store.state.main;
    },
    flowData: function() {
      return this.$store.state.project.flowData;
    },
    rightContentTargetItem: function() {
      return this.flowData.rightContentTargetItem;
    },
    permission: function() {
      return this.$store.state.global.permission;
    },
    delLimit: function() {
      var result = false;
      try {
        result = this.$.inArray("template:delete", this.permission.data) > -1;
      } catch (e) {
        console.error(e.message);
      }
      return result;
    },
    formattedCreateTime: function() {
      if (!this.item || !this.item.project || !this.item.project.createTime) {
        return "";
      }
      const date = new Date(this.item.project.createTime);
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = date.getHours();
      const minutes = ("0" + date.getMinutes()).slice(-2);
      const seconds = ("0" + date.getSeconds()).slice(-2);
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  },
  created() {
    console.log(this.$.inArray("template:delete", this.permission.data) > -1);
  },
  methods: {
    copyTemplate(ev) {
      //复制模板为自己工程
      ev.preventDefault();
      if (this.flowData.isRunning) {
        this.$notify({
          type: "warning",
          message: "工程运行中，请稍后再试！"
        });
        return;
      }
      //设置添加位置
      this.flowData.rightContentTargetItem = this.$store.state.project.projectMenu[0];
      //显示弹框
      this.menuMain.showDetail.copyTemplateDialog = true;
      //设置复制项
      this.menuMain.currentTemplate = this.deepCopy(this.item);
    },
    deepCopy: function(source) {
      var result;
      source instanceof Array
        ? (result = [])
        : typeof source === "object"
          ? source === null
            ? (result = "")
            : (result = {})
          : (result = source);
      for (var key in source) {
        result[key] =
          typeof source[key] === "object"
            ? this.deepCopy(source[key])
            : source[key];
      }
      return result;
    },
    deleteTemplate: function(ev) {
      ev.preventDefault();
      this.$confirm("此操作将删除该模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.sureDelete();
        })
        .catch(() => {});
    },
    sureDelete: function() {
      var that = this;
      this.$.ajax({
        url:
          that.$store.state.global.httpServer + "/api/template/" + that.item.id,
        method: "delete",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        beforeSend: function(xhr) {
          xhr.setRequestHeader("accessToken", localStorage.accessToken);
          setAppKeyHeaders(xhr);
        },
        success: function(data) {
          switch (data.status) {
            case "SUCCESS":
              that.$notify({
                message: "删除成功！",
                type: "success"
              });
              that.$store.dispatch("getTemplateList", 0);
              break;
            case "FAIL":
              that.$notify.error({
                title: "错误",
                message: data.message
              });
              break;
          }
        },
        error: function(response) {
          that.$store.commit("dealRequestError", response);
        }
      });
    }
  }
};
</script>
