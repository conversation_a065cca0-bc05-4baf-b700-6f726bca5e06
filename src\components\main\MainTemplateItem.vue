<template>
    <a href="javascript:void(0)" class="pListLi" @mouseover="showDeleteIcon = true" @mouseout="showDeleteIcon = false">
        <template v-if="true">
            <div class="close" v-show="showDeleteIcon" @click="deleteTemplate($event)"><i
                    class="el-icon-circle-close-outline"></i>
            </div>
        </template>
        <div @click="copyTemplate($event)" class="ppl">
            <div class="header-section">
                <span class="icon">
                    <img src="../../assets/css/images/icon-box.png" alt="">
                </span>
                <div class="title-section">
                    <span class="title">{{ item.project.name }}</span>
                    <span class="tags">{{ item.tags }}</span>
                </div>
            </div>

            <div class="desc" :title="item.project.description">
                {{ item.project.description }}
            </div>

            <div class="footer-section">
                <div class="author">
                    <!-- 圆形头像 使用 creatorName 的第一个字母 -->
                    <span class="avatar">{{ item.project.creatorName.substring(0, 1) }}</span>
                    <span class="author-name">{{ item.project.creatorName }}</span>
                </div>
                <span class="create-time">{{ formattedCreateTime }}</span>
            </div>

            <div class="copy"><span class="animated"><i class="fa fa-clipboard"></i>从模板新建</span></div>
        </div>
    </a>
</template>
<style>
/* 主容器样式 - 使用垂直flex布局，防止换行 */
.ppl {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;
}

/* 头部区域 - 图标和标题 */
.header-section {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-shrink: 0;
  margin-bottom: 8px;
}

.icon {
  margin-right: 10px;
  flex-shrink: 0;
}

.icon img {
  width: 50px;
  height: 50px;
  display: block;
}

.title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.tags {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 描述区域 */
.desc {
  color: #a7a7a7;
  font-size: 12px;
  line-height: 18px;
  height: 54px;
  overflow: hidden;
  margin: 8px 0;
  flex-shrink: 0;
  /* 限制显示行数 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

/* 底部区域 - 作者和时间 */
.footer-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: auto;
  padding: 8px 5px 0 5px; /* 上边距和左右内边距 */
  height: 32px; /* 固定高度 */
  box-sizing: border-box;
}

.author {
  display: flex;
  align-items: center;
  color: #666;
  flex: 1;
  min-width: 0; /* 允许收缩 */
  overflow: hidden;
}

.avatar {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 24px;
  margin-right: 6px;
  font-size: 12px;
  flex-shrink: 0;
}

.author-name {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.create-time {
  color: #999;
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: right;
  margin-left: 8px; /* 与作者区域的间距 */
}

/* 复制按钮覆盖层 */
.copy {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: none;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.copy span {
  display: inline-block;
  color: #408fcc;
  padding: 5px 12px;
  line-height: 24px;
}

.copy span i {
  display: block;
  font-size: 36px;
  margin-bottom: 6px;
}

/* 悬停效果 */
.pListLi:hover .copy {
  display: flex;
}

.pListLi:hover .copy span {
  animation: fadeInUps 0.3s;
}

/* 响应式媒体查询 */
@media (max-width: 1200px) {
  .ppl {
    padding: 8px;
  }

  .icon img {
    width: 40px;
    height: 40px;
  }

  .title {
    font-size: 13px;
    line-height: 18px;
  }

  .desc {
    height: 45px;
    font-size: 11px;
    line-height: 16px;
  }
}

@media (max-width: 768px) {
  .ppl {
    padding: 6px;
  }

  .header-section {
    margin-bottom: 6px;
  }

  .icon {
    margin-right: 8px;
  }

  .icon img {
    width: 35px;
    height: 35px;
  }

  .title {
    font-size: 12px;
    line-height: 16px;
  }

  .tags {
    font-size: 11px;
  }

  .desc {
    height: 40px;
    font-size: 10px;
    line-height: 14px;
    margin: 6px 0;
  }

  .footer-section {
    height: 28px;
    padding: 6px 2px 0 2px;
  }

  .avatar {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    margin-right: 4px;
  }

  .author-name {
    font-size: 11px;
  }

  .create-time {
    font-size: 10px;
    margin-left: 6px;
  }
}

@media (max-width: 576px) {
  .ppl {
    padding: 5px;
  }

  .desc {
    height: 35px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .footer-section {
    height: auto;
    flex-direction: column;
    align-items: flex-start;
    padding: 4px 2px 0 2px;
    gap: 4px;
  }

  .create-time {
    align-self: flex-end;
    margin-left: 0;
  }
}
</style>
<script>
import { setAppKeyHeaders } from "@/utils/appkey.js";
export default {
  name: "MainTemplateItem",
  props: ["item"],
  data() {
    return {
      showDeleteIcon: false //是否显示删除图标
    };
  },
  computed: {
    menuMain: function() {
      return this.$store.state.main;
    },
    flowData: function() {
      return this.$store.state.project.flowData;
    },
    rightContentTargetItem: function() {
      return this.flowData.rightContentTargetItem;
    },
    permission: function() {
      return this.$store.state.global.permission;
    },
    delLimit: function() {
      var result = false;
      try {
        result = this.$.inArray("template:delete", this.permission.data) > -1;
      } catch (e) {
        console.error(e.message);
      }
      return result;
    },
    formattedCreateTime: function() {
      if (!this.item || !this.item.project || !this.item.project.createTime) {
        return "";
      }
      const date = new Date(this.item.project.createTime);
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = date.getHours();
      const minutes = ("0" + date.getMinutes()).slice(-2);
      const seconds = ("0" + date.getSeconds()).slice(-2);
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  },
  created() {
    console.log(this.$.inArray("template:delete", this.permission.data) > -1);
  },
  methods: {
    copyTemplate(ev) {
      //复制模板为自己工程
      ev.preventDefault();
      if (this.flowData.isRunning) {
        this.$notify({
          type: "warning",
          message: "工程运行中，请稍后再试！"
        });
        return;
      }
      //设置添加位置
      this.flowData.rightContentTargetItem = this.$store.state.project.projectMenu[0];
      //显示弹框
      this.menuMain.showDetail.copyTemplateDialog = true;
      //设置复制项
      this.menuMain.currentTemplate = this.deepCopy(this.item);
    },
    deepCopy: function(source) {
      var result;
      source instanceof Array
        ? (result = [])
        : typeof source === "object"
          ? source === null
            ? (result = "")
            : (result = {})
          : (result = source);
      for (var key in source) {
        result[key] =
          typeof source[key] === "object"
            ? this.deepCopy(source[key])
            : source[key];
      }
      return result;
    },
    deleteTemplate: function(ev) {
      ev.preventDefault();
      this.$confirm("此操作将删除该模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.sureDelete();
        })
        .catch(() => {});
    },
    sureDelete: function() {
      var that = this;
      this.$.ajax({
        url:
          that.$store.state.global.httpServer + "/api/template/" + that.item.id,
        method: "delete",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        beforeSend: function(xhr) {
          xhr.setRequestHeader("accessToken", localStorage.accessToken);
          setAppKeyHeaders(xhr);
        },
        success: function(data) {
          switch (data.status) {
            case "SUCCESS":
              that.$notify({
                message: "删除成功！",
                type: "success"
              });
              that.$store.dispatch("getTemplateList", 0);
              break;
            case "FAIL":
              that.$notify.error({
                title: "错误",
                message: data.message
              });
              break;
          }
        },
        error: function(response) {
          that.$store.commit("dealRequestError", response);
        }
      });
    }
  }
};
</script>
