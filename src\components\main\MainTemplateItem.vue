<template>
    <a href="javascript:void(0)" class="pListLi" @mouseover="showDeleteIcon = true" @mouseout="showDeleteIcon = false">
        <template v-if="true">
            <div class="close" v-show="showDeleteIcon" @click="deleteTemplate($event)"><i
                    class="el-icon-circle-close-outline"></i>
            </div>
        </template>
        <div @click="copyTemplate($event)" style="display: flex;" class="ppl">
            <div style="display: flex;justify-content: flex-start;padding: 10px;">
                <span class="icon" style="margin-right: 10px; ">
                    <img src="../../assets/css/images/icon-box.png" alt="" style="width: 50px;height: 50px;">
                </span>
                <div style="display: flex;flex-direction: column">
                    <span class="title">{{ item.project.name }}</span>
                    <span>{{ item.tags }}</span>
                </div>
            </div>

            <div class="desc" style="color: #a7a7a7;margin-top: 10px;" :title="item.project.description">
                {{ item.project.description }}
            </div>

            <div style="display: flex;align-items: baseline;justify-content: space-between; font-size: 12px;">
                <div class="author">
                    <!-- 圆形头像 使用 creatorName 的第一个字母 -->
                    <span class="avatar">{{ item.project.creatorName.substring(0, 1) }}</span>
                </div>
                <span class="create-time">{{ formattedCreateTime }}</span>
            </div>

            <div class="copy"><span class="animated"><i class="fa fa-clipboard"></i>从模板新建</span></div>
        </div>
    </a>
</template>
<style>
.avatar {
    display: inline-block;
    width: 30px;
    /* Adjust size as needed */
    height: 30px;
    /* Adjust size as needed */
    border-radius: 50%;
    background-color: #409EFF;
    /* Example background color */
    color: white;
    text-align: center;
    line-height: 30px;
    /* Vertically center text */
    margin-right: 8px;
    /* Add some space between avatar and name */
    font-size: 14px;
    /* Adjust font size as needed */
    flex-shrink: 0;
    /* Prevent shrinking in flex container */
}

.full-name {
    font-size: 14px;
    /* Adjust font size as needed */
    color: #333;
    /* Example text color */
}

.create-time {
    color: #999;
    /* Example text color */
    font-size: 12px;
    /* Adjust font size as needed */
}

.ppl {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
}
</style>
<script>
import { setAppKeyHeaders } from '@/utils/appkey.js';
export default {
    name: "MainTemplateItem",
    props: ["item"],
    data() {
        return {
            showDeleteIcon: false//是否显示删除图标
        }
    },
    computed: {
        menuMain: function () {
            return this.$store.state.main;
        },
        flowData: function () {
            return this.$store.state.project.flowData;
        },
        rightContentTargetItem: function () {
            return this.flowData.rightContentTargetItem;
        },
        permission: function () {
            return this.$store.state.global.permission;
        },
        delLimit: function () {
            var result = false;
            try {
                result = (this.$.inArray('template:delete', this.permission.data) > -1);
            } catch (e) {
                console.error(e.message)
            }
            return result;
        },
        formattedCreateTime: function () {
            if (!this.item || !this.item.project || !this.item.project.createTime) {
                return '';
            }
            const date = new Date(this.item.project.createTime);
            const year = date.getFullYear();
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const day = ('0' + date.getDate()).slice(-2);
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const seconds = ('0' + date.getSeconds()).slice(-2);
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    },
    created() {
        console.log(this.$.inArray('template:delete', this.permission.data) > -1)
    },
    methods: {
        copyTemplate(ev) {//复制模板为自己工程
            ev.preventDefault();
            if (this.flowData.isRunning) {
                this.$notify({
                    type: "warning",
                    message: "工程运行中，请稍后再试！"
                });
                return;
            }
            //设置添加位置
            this.flowData.rightContentTargetItem = this.$store.state.project.projectMenu[0];
            //显示弹框
            this.menuMain.showDetail.copyTemplateDialog = true;
            //设置复制项
            this.menuMain.currentTemplate = this.deepCopy(this.item);
        },
        deepCopy: function (source) {
            var result;
            (source instanceof Array) ? (result = []) : (typeof (source) === "object") ? (source === null ? (result = "") : (result = {})) : (result = source);
            for (var key in source) {
                result[key] = (typeof source[key] === 'object') ? this.deepCopy(source[key]) : source[key];
            }
            return result;
        },
        deleteTemplate: function (ev) {
            ev.preventDefault();
            this.$confirm('此操作将删除该模板, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.sureDelete();
            }).catch(() => {
            });
        },
        sureDelete: function () {
            var that = this;
            this.$.ajax({
                url: that.$store.state.global.httpServer + "/api/template/" + that.item.id,
                method: "delete",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                beforeSend: function (xhr) {
                    xhr.setRequestHeader("accessToken", localStorage.accessToken);
                    setAppKeyHeaders(xhr);

                },
                success: function (data) {
                    switch (data.status) {
                        case "SUCCESS":
                            that.$notify({
                                message: "删除成功！",
                                type: 'success'
                            });
                            that.$store.dispatch("getTemplateList", 0);
                            break;
                        case "FAIL":
                            that.$notify.error({
                                title: "错误",
                                message: data.message
                            });
                            break;
                    }
                },
                error: function (response) {
                    that.$store.commit("dealRequestError", response);
                }
            });
        }
    }
}
</script>
