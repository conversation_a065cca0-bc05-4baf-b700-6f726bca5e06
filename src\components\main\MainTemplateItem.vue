<template>
    <a href="javascript:void(0)" class="pListLi" @mouseover="showDeleteIcon = true" @mouseout="showDeleteIcon = false">
        <template v-if="true">
            <div class="close" v-show="showDeleteIcon" @click="deleteTemplate($event)"><i
                    class="el-icon-circle-close-outline"></i>
            </div>
        </template>
        <div @click="copyTemplate($event)" class="ppl">
            <div class="header-section">
                <span class="icon">
                    <img src="../../assets/css/images/icon-box.png" alt="">
                </span>
                <div class="title-section">
                    <span class="title">{{ item.project.name }}</span>
                    <span class="tags">{{ item.tags }}</span>
                </div>
            </div>

            <div class="desc" :title="item.project.description">
                {{ item.project.description }}
            </div>

            <div class="footer-section">
                <div class="author">
                    <!-- 圆形头像 使用 creatorName 的第一个字母 -->
                    <span class="avatar">{{ item.project.creatorName.substring(0, 1) }}</span>
                </div>
                <span class="create-time">{{ formattedCreateTime }}</span>
            </div>

            <div class="copy"><span class="animated"><i class="fa fa-clipboard"></i>从模板新建</span></div>
        </div>
    </a>
</template>
<style>
/* 覆盖全局样式 - 防止在不同分辨率下被裁剪 */
.pList .pListLi {
  overflow: visible !important; /* 覆盖全局的 overflow: hidden */
  height: auto !important; /* 允许高度自适应 */
  min-height: 250px !important; /* 保持最小高度 */
}

/* 主容器样式 - 使用垂直flex布局，防止换行 */

/* 头部区域 - 图标和标题 */
.header-section {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-shrink: 0;
  margin-bottom: 8px;
}

.icon {
  margin-right: 10px;
  flex-shrink: 0;
}

.icon img {
  width: 50px;
  height: 50px;
  display: block;
}

.title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.tags {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 描述区域 */
.desc {
  color: #a7a7a7;
  font-size: 12px;
  line-height: 18px;
  height: 54px;
  overflow: hidden;
  margin: 8px 0;
  flex-shrink: 0;
  /* 限制显示行数 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

/* 底部区域 - 作者和时间 */
.footer-section {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: auto;
  padding: 5px;
  height: 35px; /* 增加高度以适应150%缩放 */
  box-sizing: border-box;
  /* 强制单行显示 */
  white-space: nowrap;
  overflow: visible; /* 改为visible确保内容可见 */
  /* 确保在不同分辨率下不被遮挡 */
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95); /* 添加半透明背景确保可见性 */
}

.author {
  display: flex;
  align-items: center;
  color: #666;
  flex: 0 0 30px; /* 固定宽度，只给avatar留空间 */
  min-width: 30px;
  max-width: 30px; /* 限制最大宽度，为时间留出更多空间 */
  overflow: visible;
}

.avatar {
  display: inline-block;
  width: 24px; /* 增加尺寸以适应150%缩放 */
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 24px;
  margin-right: 6px;
  font-size: 12px;
  flex-shrink: 0;
  /* 确保在高分辨率下可见 */
  min-width: 24px;
  min-height: 24px;
}

.author-name {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px; /* 限制作者名最大宽度 */
}

.create-time {
  color: #999;
  font-size: 11px;
  white-space: nowrap;
  flex: 1; /* 占用剩余空间 */
  text-align: right;
  margin-left: 8px;
  /* 防止文字超出容器 */
  min-width: 0; /* 允许弹性调整 */
  overflow: hidden; /* 防止超出容器 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  max-width: 100%; /* 限制最大宽度 */
  box-sizing: border-box;
}

/* 复制按钮覆盖层 - 覆盖全局样式 */
.pList .pListLi .copy {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(255, 255, 255, 0.9) !important;
  display: none !important;
  /* 使用flexbox居中 */
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  /* 覆盖全局样式中的line-height */
  line-height: normal !important;
}

.pList .pListLi .copy span {
  display: inline-block !important;
  color: #408fcc !important;
  padding: 5px 12px !important;
  line-height: 24px !important;
  height: auto !important; /* 覆盖全局的height: 24px */
  /* 完全重置位置，让flexbox处理居中 */
  margin: 0 !important; /* 覆盖全局的margin-top: 45% */
  position: static !important;
  transform: none !important;
}

.pList .pListLi .copy span i {
  display: block !important;
  font-size: 36px !important;
  margin-bottom: 6px !important;
}

.ppl {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;
  /* 确保在不同分辨率下内容不被裁剪 */
  min-height: 250px;
  position: relative;
}
/* 悬停效果 - 确保在所有分辨率下都能正常显示 */
.pListLi:hover .copy {
  display: flex !important; /* 使用flex确保居中 */
}

.pListLi:hover .copy span {
  animation: fadeInUps 0.3s;
}
</style>
<script>
import { setAppKeyHeaders } from "@/utils/appkey.js";
export default {
  name: "MainTemplateItem",
  props: ["item"],
  data() {
    return {
      showDeleteIcon: false //是否显示删除图标
    };
  },
  computed: {
    menuMain: function() {
      return this.$store.state.main;
    },
    flowData: function() {
      return this.$store.state.project.flowData;
    },
    rightContentTargetItem: function() {
      return this.flowData.rightContentTargetItem;
    },
    permission: function() {
      return this.$store.state.global.permission;
    },
    delLimit: function() {
      var result = false;
      try {
        result = this.$.inArray("template:delete", this.permission.data) > -1;
      } catch (e) {
        console.error(e.message);
      }
      return result;
    },
    formattedCreateTime: function() {
      if (!this.item || !this.item.project || !this.item.project.createTime) {
        return "";
      }
      const date = new Date(this.item.project.createTime);
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const hours = date.getHours();
      const minutes = ("0" + date.getMinutes()).slice(-2);
      const seconds = ("0" + date.getSeconds()).slice(-2);
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  },
  created() {
    console.log(this.$.inArray("template:delete", this.permission.data) > -1);
  },
  methods: {
    copyTemplate(ev) {
      //复制模板为自己工程
      ev.preventDefault();
      if (this.flowData.isRunning) {
        this.$notify({
          type: "warning",
          message: "工程运行中，请稍后再试！"
        });
        return;
      }
      //设置添加位置
      this.flowData.rightContentTargetItem = this.$store.state.project.projectMenu[0];
      //显示弹框
      this.menuMain.showDetail.copyTemplateDialog = true;
      //设置复制项
      this.menuMain.currentTemplate = this.deepCopy(this.item);
    },
    deepCopy: function(source) {
      var result;
      source instanceof Array
        ? (result = [])
        : typeof source === "object"
          ? source === null
            ? (result = "")
            : (result = {})
          : (result = source);
      for (var key in source) {
        result[key] =
          typeof source[key] === "object"
            ? this.deepCopy(source[key])
            : source[key];
      }
      return result;
    },
    deleteTemplate: function(ev) {
      ev.preventDefault();
      this.$confirm("此操作将删除该模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.sureDelete();
        })
        .catch(() => {});
    },
    sureDelete: function() {
      var that = this;
      this.$.ajax({
        url:
          that.$store.state.global.httpServer + "/api/template/" + that.item.id,
        method: "delete",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        beforeSend: function(xhr) {
          xhr.setRequestHeader("accessToken", localStorage.accessToken);
          setAppKeyHeaders(xhr);
        },
        success: function(data) {
          switch (data.status) {
            case "SUCCESS":
              that.$notify({
                message: "删除成功！",
                type: "success"
              });
              that.$store.dispatch("getTemplateList", 0);
              break;
            case "FAIL":
              that.$notify.error({
                title: "错误",
                message: data.message
              });
              break;
          }
        },
        error: function(response) {
          that.$store.commit("dealRequestError", response);
        }
      });
    }
  }
};
</script>
